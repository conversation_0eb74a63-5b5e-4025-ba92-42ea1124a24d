Options -Indexes

# Only redirect to login if not logged in and not already on login page
RewriteEngine On
RewriteBase /phpbookstore/admin/

# Don't rewrite existing files/directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Don't rewrite the login page or assets
RewriteCond %{REQUEST_URI} !^/phpbookstore/admin/login\.php
RewriteCond %{REQUEST_URI} !^/phpbookstore/admin/assets/

# PHP file handling
RewriteRule ^([^/]+)/?$ $1.php [L]
