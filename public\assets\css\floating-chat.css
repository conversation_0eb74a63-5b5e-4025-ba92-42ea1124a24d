.floating-icon {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 1000;
}

.floating-icon svg {
    color: #fff;
}

.floating-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.chat-widget {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 350px;
    height: 450px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    z-index: 999;
    display: none;
    flex-direction: column;
}

.chat-widget.active {
    display: flex;
}

.chat-header {
    padding: 15px;
    background-color: #4CAF50;
    color: white;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header .chat-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
}

.chat-messages {
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.chat-message {
    padding: 12px;
    margin: 8px 0;
    border-radius: 10px;
    max-width: 85%;
    line-height: 1.4;
}

.user-message {
    background-color: #e3f2fd;
    align-self: flex-end;
    margin-left: auto;
    color: #0d47a1;
}

.ai-message {
    background-color: #f5f5f5;
    align-self: flex-start;
    margin-right: auto;
    color: #333;
    white-space: pre-wrap; /* This will preserve formatting */
    line-height: 1.5;
}

.error-message {
    background-color: #ffebee;
    color: #c62828;
    align-self: center;
}

.chat-message p {
    margin: 0;
    word-break: break-word;
}

.chat-input-container {
    padding: 15px;
    border-top: 1px solid #eee;
}

.chat-input-container textarea {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 10px;
    resize: none;
    height: 60px;
}

.chat-input-container button {
    width: 100%;
    padding: 8px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.chat-input-container button:hover {
    background: #45a049;
}

/* Add some styling for math expressions */
.math-expression {
    background-color: #f8f9fa;
    padding: 5px;
    border-radius: 4px;
    font-family: monospace;
}

/* Add styling for steps or lists */
.step-list {
    margin: 5px 0;
    padding-left: 20px;
}

.step-item {
    margin: 3px 0;
}

/* Add styles for equations */
.equation {
    font-family: monospace;
    font-size: 1.1em;
    padding: 8px;
    background: #fff;
    border-radius: 4px;
    margin: 8px 0;
    text-align: center;
}

/* Add styles for steps */
.step {
    margin: 4px 0;
    padding-left: 20px;
}

/* Add these styles */
.ai-thinking {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background-color: #f5f5f5;
    align-self: flex-start;
    margin-right: auto;
    border-radius: 10px;
}

.thinking-dots {
    display: inline-flex;
    gap: 4px;
}

.thinking-dots span {
    width: 8px;
    height: 8px;
    background-color: #666;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

.thinking-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.4; }
    50% { transform: scale(1.2); opacity: 1; }
}